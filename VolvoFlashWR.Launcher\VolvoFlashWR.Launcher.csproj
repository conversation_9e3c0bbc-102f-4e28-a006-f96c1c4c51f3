<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
    <StartupObject>VolvoFlashWR.Launcher.STAProgram</StartupObject>
    <ApplicationHighDpiMode>PerMonitorV2</ApplicationHighDpiMode>
    <ApplicationUseCompatibleTextRenderingDefault>true</ApplicationUseCompatibleTextRenderingDefault>
    <ApplicationDefaultFont>Segoe UI, 9pt</ApplicationDefaultFont>
    <EnableDefaultApplicationDefinition>false</EnableDefaultApplicationDefinition>
    <!-- <ApplicationIcon>VolvoFlashWR.ico</ApplicationIcon> -->
    <Version>1.0.0</Version>
    <Authors>S.A.H Software Solutions</Authors>
    <Company>S.A.H Software Solutions</Company>
    <Product>Volvo Flash WR</Product>
    <Description>ECU Management Tool for Volvo vehicles</Description>
    <Copyright>© 2025 All Rights Reserved For S.A.H Software Solutions Company</Copyright>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>false</SelfContained>
    <RuntimeIdentifier>win-x86</RuntimeIdentifier>
    <PlatformTarget>x86</PlatformTarget>
    <PublishReadyToRun>false</PublishReadyToRun>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\VolvoFlashWR.UI\VolvoFlashWR.UI.csproj" />
  </ItemGroup>

  <!-- <ItemGroup>
    <None Remove="VolvoFlashWR.ico" />
    <Content Include="VolvoFlashWR.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup> -->

</Project>
